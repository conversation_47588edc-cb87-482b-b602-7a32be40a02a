import React, { useEffect, useState } from "react";
import InformationContainer from "./InformationContainer";
import {
  Typography, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Checkbox
} from "@mui/material";

type AllergyRow = {
  id: string;
  name: string;
  examples: string;
  checked: boolean;
};

const DEFAULT_ROWS: AllergyRow[] = [
  { id: "iodine",   name: "Produit de contraste iodé",           examples: "Iode, Visipaque, Omnipaque", checked: false },
  { id: "gadolinium", name: "Produit de contraste IRM (Gadolinium)", examples: "Dotarem, ProHance, Gadavist", checked: false },
  { id: "local",    name: "Anesthésiques locaux",                 examples: "Lidocaïne, xylocaïne, bupivacaïne", checked: false },
  { id: "sedatives",name: "Sédatifs",                             examples: "Midazolam, fentanyl, morphine", checked: false },
  { id: "latex",    name: "Latex",                                examples: "Gants, cathéters, tubulures", checked: false },
  { id: "nsaids",   name: "AINS / Aspirine",                      examples: "Ibuprofène, naproxène, aspirine", checked: false },
  { id: "food",     name: "Aliments pertinents / excipients",     examples: "Fruits de mer, œufs/soja (propofol)", checked: false },
  { id: "other",    name: "Autres allergies importantes",         examples: "Chlorhexidine, médicaments spécifiques, etc.", checked: false },
];

export default function InformationAllergiesForm() {
  const [rows, setRows] = useState<AllergyRow[]>(() => {
    const saved = localStorage.getItem("allergies");
    return saved ? JSON.parse(saved) as AllergyRow[] : DEFAULT_ROWS;
  });

  // Chauq fois que le checkbox change ça va les mettre dans le localstorage. Toutes les allergies y apparaissent. le checked true/false change.
  useEffect(() => {
    localStorage.setItem("allergies", JSON.stringify(rows));
  }, [rows]);

  const toggleRow = (id: string, next: boolean) => {
    setRows(prev => prev.map(r => r.id === id ? { ...r, checked: next } : r));
  };

  // Exemple de post (À adapter avec le backend avec les autres.)
  const buildPayload = () =>
    rows.map(r => ({ code: r.id, name: r.name, hasIntolerance: r.checked }));

  return (
    <InformationContainer title="Allergies">
      <TableContainer
        sx={{
            '& th:nth-of-type(odd)': { backgroundColor: 'text.secondary'}, 
    '& th:nth-of-type(even)': { backgroundColor: 'text.secondary'},  
    '& tr:nth-of-type(odd)': { backgroundColor: 'TableCell.border' },   
    '& tr:nth-of-type(even)': { backgroundColor: 'text.secondary' },
    maxHeight: '45vh'
        }}
      >
        <Table size="small" stickyHeader>
          <TableHead>
            <TableRow>
              <TableCell><Typography variant="body1" sx={{ fontWeight: 600 }}>Allergie</Typography></TableCell>
              <TableCell><Typography variant="body1" sx={{ fontWeight: 600 }}>Exemples de produits</Typography></TableCell>
              <TableCell align="center"><Typography variant="body1" sx={{ fontWeight: 600 }}>Oui / Non (cocher)</Typography></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {rows.map(row => (
              <TableRow key={row.id}>
                <TableCell><Typography variant="body1" component="span">{row.name}</Typography></TableCell>
                <TableCell><Typography variant="body1" component="span">{row.examples}</Typography></TableCell>
                <TableCell align="center">
                  <Checkbox
                    checked={row.checked}
                    onChange={(e) => toggleRow(row.id, e.target.checked)}
                    sx={{
                      '& .MuiSvgIcon-root': { fontSize: 32 },
                    }}
                    slotProps={{ input: { 'aria-label': `${row.name} intolerance` } }}
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      {/* Exemple d’utilisation future: */}
      {/* <Button onClick={() => api.save(buildPayload())}>Enregistrer</Button> */}
    </InformationContainer>
  );
}
