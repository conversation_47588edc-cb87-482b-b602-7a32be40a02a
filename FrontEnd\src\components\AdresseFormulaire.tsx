import { Box, TextField } from '@mui/material';
import InformationContainer from './InformationContainer';
import React, { useEffect, useState } from "react";
import type { Addresse } from '../types/ClientData';

//Affichage du formulaire avec des champs (barrés, non barrés)
 
// Composant principal du formulaire d'adresse (étape 2 du parcours)
// Affiche les champs d'adresse dans un conteneur stylisé
export default function AdresseFormulaire({ adresse }: { adresse: any }) {
  const [address, setAddresses] = useState<Addresse>(adresse);

  useEffect(() => {
    sessionStorage.setItem("address", JSON.stringify(address));
  }, [address]);

  return (
    <InformationContainer title="Adresse">
      <Box display="flex" flexDirection="column" gap={2} width="100%" sx={{padding: 2}}>
        <Box display="flex" gap={2} width="100%">
          <TextField
            label="No civique"
            variant="outlined"
            value={address.civicNumber}
            sx={{flex: 1, background: 'white', borderRadius: '30px'}}
            slotProps={{ input: { style: { borderRadius: 30, color: '#000' }, readOnly: false }, inputLabel: { style: { color: '#000' } } }}
            onChange={(e) => {
              if (localStorage.getItem("modifiable") == "false") return;
              address.civicNumber = e.target.value;
              setAddresses({...address, civicNumber: e.target.value });
            }}
          />
          <TextField
            label="No App"
            variant="outlined"
            value="N'existe pas pour l'instant dans la BD"
            sx={{flex: 1, background: 'white', borderRadius: '30px'}}
            slotProps={{ input: { style: { borderRadius: 30, color: '#000' } }, inputLabel: { style: { color: '#000' } } }}
          />
        </Box>
        <Box display="flex" gap={2} width="100%">
          <TextField
            label="Nom de la rue"
            variant="outlined"
            value={address.street}
            sx={{flex: 1, background: 'white', borderRadius: '30px'}}
            slotProps={{ input: { style: { borderRadius: 30, color: '#000' } }, inputLabel: { style: { color: '#000' } } }}
            onChange={(e) => {
              if (localStorage.getItem("modifiable") == "false") return;
              address.street = e.target.value;
              setAddresses({...address, street: e.target.value });
            }}
          />
          <TextField
            label="Ville"
            variant="outlined"
            value={address.city}
            sx={{flex: 1, background: 'white', borderRadius: '30px'}}
            slotProps={{ input: { style: { borderRadius: 30, color: '#000' } }, inputLabel: { style: { color: '#000' } } }}
            onChange={(e) => {
              if (localStorage.getItem("modifiable") == "false") return;
              address.city = e.target.value;
              setAddresses({ ...address, city: e.target.value });
            }}
          />
        </Box>
        <Box display="flex" gap={2} width="100%">
          <TextField
            label="Province"
            variant="outlined"
            value={address.state}
            sx={{flex: 1, background: 'white', borderRadius: '30px'}}
            slotProps={{ 
              input: { style: { borderRadius: 30, color: '#000' } },
              inputLabel: { style: { color: '#000' } },
              htmlInput: { maxLength: 2 } 
            }}

            onChange={(e) => {
              if (localStorage.getItem("modifiable") == "false") return;
              address.state = e.target.value;
              setAddresses({ ...address, state: e.target.value });
            }}
          />
          <TextField
            label="Code postal"
            variant="outlined"
            value={address.postalCode}
            sx={{flex: 1, background: 'white', borderRadius: '30px'}}
            slotProps={{
              input: { style: { borderRadius: 30, color: '#000' } },
              inputLabel: { style: { color: '#000' } },
              htmlInput: { maxLength: 7 } 
            }}
            onChange={(e) => {
              if (localStorage.getItem("modifiable") == "false") return;
              address.postalCode = e.target.value;
              setAddresses({ ...address, postalCode: e.target.value });
            }}
          />
        </Box>
      </Box>
    </InformationContainer>
  );
}