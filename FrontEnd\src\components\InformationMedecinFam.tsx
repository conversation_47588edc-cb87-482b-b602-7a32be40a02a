import React from "react";
import InformationContainer from "./InformationContainer";
import { TextField, Box, Typography } from "@mui/material";

export default function MedecinFamForm( { medecinPrenom, medecinNom,  } : { medecinPrenom: any, medecinNom: any, } ) {
  return (
    <InformationContainer
      title={
          "Médecin de famille"
      }
    >
      <Box
        sx={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "space-between",
          width: "100%",
          mb: 0,
          padding: 2
        }}
      >
        <TextField
          label="Prénom"
          variant="outlined"
          sx={{ width: "49%" }}
          InputLabelProps={{ sx: { color: "gray" } }}
          value={medecinPrenom}
        />
        <TextField
          label="Nom"
          variant="outlined"
          sx={{ width: "49%" }}
          InputLabelProps={{ sx: { color: "gray" } }}
          value={medecinNom}
        />
      </Box>
    </InformationContainer>
  );
}
