import React, { useState, useEffect } from "react";
import { Box, Typography, Grid } from "@mui/material";
import InformationContainer from "./InformationContainer";
import type { Addresse } from '../types/ClientData';

export default function ResumeDossier( { adresse, telephone, medecinPrenom, medecinNom, /*contactUrgence,*/ prenomMere, nomMere, prenomPere, nomPere, email }: { adresse: any, telephone: any, medecinPrenom: any, medecinNom: any, /*contactUrgence: any,*/ prenomMere: any, nomMere: any, prenomPere: any, nomPere: any, email: any }) {
  const [address, setAddresses] = useState<Addresse>(adresse);

    useEffect(() => {
      sessionStorage.setItem("address", JSON.stringify(address));
    }, [address]);
    //Le "tableau" reçoit les données de l'API et la manière qu'il s'affiche c'est ligne par ligne. Après la box principale, les box constituent chacune une ligne dans le tableau.
  return (
    <InformationContainer title="Résumé du dossier" displayButton={false}>
      <Box sx={{ display: 'flex', flexDirection: 'column' , width: '100%', padding: 2}}>

        <Box sx={{ display: 'flex', flexDirection: 'row', width: '100%', mb:2}}>
          <Typography variant="body2" fontWeight={600} fontSize={"24px"} width={'50%'}>Adresse</Typography>
          <Typography variant="body2" fontSize={"24px"} width={'50%'}>{adresse.civicNumber} rue {adresse.street} {adresse.city} {adresse.state} {adresse.postalCode}</Typography>
        </Box>

        <Typography variant="body2" fontWeight={600} fontSize={"24px"}>Informations de contact</Typography>

        <Box sx={{ display: 'flex', flexDirection: 'row', width: '100%',}}>
          <Typography variant="body2" fontSize={"24px"} width={'50%'} paddingLeft={2}>Téléphone(s):</Typography>
          <Typography variant="body2" fontSize={"24px"} width={'50%'}>{telephone && telephone.map((phone: any) => phone.number).join(', ')}</Typography>
        </Box>
        <Box sx={{ display: 'flex', flexDirection: 'row', width: '100%', mb:2}}>
          <Typography variant="body2" fontSize={"24px"} width={'50%'} paddingLeft={2}>Email:</Typography>
          <Typography variant="body2" fontSize={"24px"} width={'50%'}>{email}</Typography>
        </Box>
        <Typography variant="body2" fontWeight={600} fontSize={"24px"}>Information sur les parents</Typography>
        <Box sx={{ display: 'flex', flexDirection: 'row', width: '100%', }}>
          <Typography variant="body2" fontSize={"24px"} width={'50%'} paddingLeft={2}>Nom du père:</Typography>
          <Typography variant="body2" fontSize={"24px"} width={'50%'}>{prenomPere} {nomPere}</Typography>
        </Box>
        <Box sx={{ display: 'flex', flexDirection: 'row', width: '100%', mb:2}}>
          <Typography variant="body2" fontSize={"24px"} width={'50%'} paddingLeft={2}>Nom de la mère:</Typography>
          <Typography variant="body2" fontSize={"24px"} width={'50%'}>{prenomMere} {nomMere}</Typography>
        </Box>

        <Box sx={{ display: 'flex', flexDirection: 'row', width: '100%', mb:2}}>
          <Typography variant="body2" fontWeight={600} fontSize={"24px"} width={'50%'}>Médecin de famille</Typography>
          <Typography variant="body2" fontSize={"24px"} width={'50%'}>{medecinPrenom} {medecinNom}</Typography>
        </Box>

        <Typography variant="body2" fontWeight={600} fontSize={"24px"}>Contact d'urgence</Typography>
        <Box sx={{ display: 'flex', flexDirection: 'row', width: '100%', }}>
          <Typography variant="body2" fontSize={"24px"} width={'50%'} paddingLeft={2}>Nom:</Typography>
          <Typography variant="body2" fontSize={"24px"} width={'50%'}>{prenomMere} {nomMere}</Typography>
        </Box>
        <Box sx={{ display: 'flex', flexDirection: 'row', width: '100%', }}>
          <Typography variant="body2" fontSize={"24px"} width={'50%'} paddingLeft={2}>Téléphone:</Typography>
          <Typography variant="body2" fontSize={"24px"} width={'50%'}>{telephone && telephone.map((phone: any) => phone.number).join(', ')}</Typography>
        </Box>
        <Box sx={{ display: 'flex', flexDirection: 'row', width: '100%', }}>
          <Typography variant="body2" fontSize={"24px"} width={'50%'} paddingLeft={2}>Relation:</Typography>
          <Typography variant="body2" fontSize={"24px"} width={'50%'}>{"relation"}</Typography>
        </Box>
        
      </Box>
    </InformationContainer>
    );
}