import React, { useEffect, useState } from "react";
import { Typography, Paper } from "@mui/material";
import { fetchUserAppointments } from "../api/rdv"

export default function Rdv() {
    useEffect(() => {
        
        const getRdvResult = async () => {
            //fetchUserAppointments();
        }
    }, []);

   return (
      <Paper sx={{ p: 3 }}>
         <Typography variant="h5" gutterBottom>Statut du backend 📶</Typography>
         <Typography>Réponse : {status}</Typography>
      </Paper>
   );
}