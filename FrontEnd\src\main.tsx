import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App.tsx";
import Header from "./components/Header";
import { CssBaseline, ThemeProvider, colors, createTheme } from "@mui/material";
import Barlowttf from "../fonts/Barlow-Regular.ttf"; // <PERSON> font
import BarlowSemiBoldttf from "../fonts/Barlow-SemiBold.ttf"; // <PERSON> font, but semibold
import CustomChevron from "../images/chevron-down.svg";


const theme = createTheme({
  colorSchemes: {
    light: {
      palette: {
        mode: "light",
        primary: { main: "#edeae6" },
        secondary: { main: "#7e9daa" },
        background: { default: "#fffefb" },
        action: { active: "#272A74", hover: "#272a74", selected: "#ffe8a2" },
        text: { primary: "#1b181c", secondary: "#fffefb" },
        TableCell: { border: '#D9D9D9' },
      }
      },
    dark: {
      palette: {
        mode: "dark",
        primary: { main: "#1c2937" },
        secondary: { main: "#191c25",  },
        background: { default: "#191c25" },
        action: { hover: "#272a74", active: "#b17de6", selected: "#3B6590"},
        text: { primary: "#fffefb", secondary: "#3B6590", disabled: "Un genre de gris" },
        TableCell: { border: '#20354B' },
      }
    }
  },
  shape: { borderRadius: 12.507 },
  typography: { fontFamily: "Barlow, sans-serif" },
  components: {
    MuiTextField: {
      defaultProps: {
        variant: "outlined",
        size: "medium",
      },
    },
    MuiOutlinedInput: {
      styleOverrides: {
        root: ({ theme }) => ({
          borderRadius: 9999,
          backgroundColor: theme.palette.text.secondary,
          "&.Mui-disabled": {
            backgroundColor: theme.palette.text.secondary,
          },
          "& .MuiOutlinedInput-notchedOutline": {
            borderColor: '#000000',
            borderWidth: "1.5px",
          },
          "&:hover .MuiOutlinedInput-notchedOutline": {
            borderColor: '#000000',
          },
          "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
            borderColor: '#000000',
            borderWidth: "2px",
          },
          "&.Mui-focused": {
            boxShadow: "none",
          },
        }),
        input: ({ theme }) => ({
          fontFamily: "Barlow, inherit",
          padding: "12px 16px",
          color: theme.palette.text.primary,
        }),
      },
    },
    MuiInputLabel: {
      styleOverrides: {
        root: {
          fontFamily: "Barlow, sans-serif",
          "&.Mui-focused": {
            color: "#000000",
          },
          color: "#000000",
        },
      },
    },
    MuiSelect: {
      defaultProps: {
        // Custom icon component with vertical separator
        IconComponent: (props) => (
          <span {...props} style={{ 
            ...props.style,
            right: 0,
            width: 64,
            height: 'calc(100% - 4px)',
            top: 2,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            pointerEvents: 'none',
            position: 'absolute',
            borderLeft: '1px solid',
            borderColor: theme.palette.text.primary,
            backgroundColor: 'transparent',
            borderTopRightRadius: 9999,
            borderBottomRightRadius: 9999,
          }}>
            <img 
              src={CustomChevron} 
              alt="select arrow" 
              style={{
                width: 24,
                height: 24,
                transition: 'transform 0.2s',
                transform: props.className?.includes('MuiSelect-iconOpen') ? 'rotate(180deg)' : 'rotate(0deg)',
              }}
            />
          </span>
        ),
        MenuProps: {
          anchorOrigin: { vertical: "bottom", horizontal: "left" },
          transformOrigin: { vertical: "top", horizontal: "left" },
          PaperProps: {
            elevation: 0,
            sx: (theme) => ({
              border: `1.5px solid ${theme.palette.text.primary}`, // <-- full menu border
              borderRadius: '20px',
              boxShadow: 'none',
              backgroundColor: theme.palette.background.paper || '#ffffff',
              mt: 0,
              overflow: 'hidden',
              '& .MuiList-root': { p: 0 },
              '& .MuiMenuItem-root': {
                fontFamily: 'Barlow, sans-serif',
                fontSize: 14,
                p: 1,                 // no padding
                m: '2px 0',
                minHeight: 'auto',
                color: theme.palette.text.primary,
                transition: 'all .2s ease',
                '&:hover': { backgroundColor: theme.palette.action.selected },
                '&.Mui-selected': {
                  backgroundColor: theme.palette.action.selected,
                  fontWeight: 500,
                  '&:hover': { backgroundColor: theme.palette.action.selected },
                },
              },
              '& .MuiDivider-root': {
                m: '4px 12px',
                borderColor: 'rgba(0,0,0,0.08)',
              },
              '& .MuiMenuItem-root.Mui-selected, \
                & .MuiMenuItem-root.Mui-selected:hover, \
                & .MuiMenuItem-root.Mui-selected.Mui-focusVisible, \
                & .MuiMenuItem-root.Mui-selected.Mui-focusVisible:hover': {
                backgroundColor: theme.palette.action.selected,
              },
            }),
          },
        },
      },
    },
    MuiCheckbox: {
      styleOverrides: {
        root: ({ theme }) => ({
          color: theme.palette.text.primary,
          '&.Mui-checked': {
            color: theme.palette.action.active,
          },
        }),
      },
    },
    MuiCssBaseline: {
      styleOverrides: `
        @font-face {
          font-family: 'Barlow';
          font-style: normal;
          font-display: swap;
          font-weight: 400;
          src: local('Barlow'), local('Barlow-Regular'), url(${Barlowttf}) format('truetype');
          unicodeRange: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF;
        }
        @font-face {
          font-family: 'Barlow';
          font-style: normal;
          font-display: swap;
          font-weight: 600;
          src: local('Barlow SemiBold'), local('Barlow-SemiBold'), url(${BarlowSemiBoldttf}) format('truetype');
          unicodeRange: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF;
        }
      `,
    },
  },

});

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <App />
    </ThemeProvider>
  </React.StrictMode>
);
