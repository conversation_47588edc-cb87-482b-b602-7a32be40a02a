import axios from "axios"
import type { AxiosResponse } from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL;

/**
 * Récupère les informations d'un utilisateur via son NAM
 * @param nam - Le numéro d'assurance maladie (identifiant)
 * @returns Les données utilisateur (JSON)
 */
export const fetchUserInfoByNAM = async (nam: string) => {
    try {
      // Appel GET à l'API backend /ramq/{nam}
      const response: AxiosResponse<any> = await axios.get(`${API_BASE_URL}/api/patients/${encodeURIComponent(nam)}`, {
        headers: {
          'Accept': 'application/json', // On attend une réponse JSON
        },
      });

      const data = response.data;
  
      return data;
  
    } catch (error: any) {
      console.error('Erreur API:', error.response?.status, error.message);
      throw error;
    }
};