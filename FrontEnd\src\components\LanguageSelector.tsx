import { Box, Typography } from "@mui/material";
import { useState } from "react";

//Sélectionne la langue (non fonctionnel)
const LanguageSelector = () => {
    const [language, setLanguage] = useState('FR');
  
    return (
      <Box 
        onClick={() => setLanguage(language === 'FR' ? 'EN' : 'FR')}
        sx={{ 
          cursor: 'pointer',
          fontFamily: 'Barlow',
          fontSize: '16px',
          userSelect: 'none'
        }}
      >
        <Typography 
          component="span" 
          sx={{ 
            fontWeight: language === 'FR' ? 600 : 400,
            fontFamily: '<PERSON>',
            fontSize: '32px'
          }}
        >
          FR
        </Typography>
        <Typography component="span" fontSize={'32px'} sx={{ mx: 1, fontFamily: 'Barlow' }}>
          
        </Typography>
        <Typography 
          component="span" 
          sx={{ 
            fontWeight: language === 'EN' ? 600 : 400,
            fontFamily: '<PERSON>',
            fontSize: '32px'
          }}
        >
          EN
        </Typography>
      </Box>
    );
  };

  export default LanguageSelector;