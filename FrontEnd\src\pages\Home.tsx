import React, { useEffect, useState } from "react";
import { Typography, Paper } from "@mui/material";
import http from "../api/http";

export default function Home() {
   const [status, setStatus] = useState<string>("...");

   useEffect(() => {
      http.get("/api/health").then(res => setStatus(res.data.status)).catch(() => setStatus("ERREUR"));
   }, []);

   return (
      <Paper sx={{ p: 3 }}>
         <Typography variant="h5" gutterBottom>Statut du backend 📶</Typography>
         <Typography>Réponse : {status}</Typography>
      </Paper>
   );
}