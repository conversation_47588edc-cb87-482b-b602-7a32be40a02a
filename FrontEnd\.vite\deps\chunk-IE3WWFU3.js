import {
  require_react
} from "./chunk-32EALFBN.js";
import {
  __toESM
} from "./chunk-G3PMV62Z.js";

// node_modules/@mui/material/esm/FormControl/useFormControl.js
var React2 = __toESM(require_react(), 1);

// node_modules/@mui/material/esm/FormControl/FormControlContext.js
var React = __toESM(require_react(), 1);
var FormControlContext = React.createContext(void 0);
if (true) {
  FormControlContext.displayName = "FormControlContext";
}
var FormControlContext_default = FormControlContext;

// node_modules/@mui/material/esm/FormControl/useFormControl.js
function useFormControl() {
  return React2.useContext(FormControlContext_default);
}

// node_modules/@mui/material/esm/FormControl/formControlState.js
function formControlState({
  props,
  states,
  muiFormControl
}) {
  return states.reduce((acc, state) => {
    acc[state] = props[state];
    if (muiFormControl) {
      if (typeof props[state] === "undefined") {
        acc[state] = muiFormControl[state];
      }
    }
    return acc;
  }, {});
}

export {
  formControlState,
  FormControlContext_default,
  useFormControl
};
//# sourceMappingURL=chunk-IE3WWFU3.js.map
