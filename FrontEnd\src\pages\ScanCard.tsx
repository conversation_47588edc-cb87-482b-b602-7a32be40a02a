import React, { useEffect, useState } from 'react';
import { SaveData } from "../utils/SaveData";
import { RAMQdecoder, type CardData } from "../utils/RAMQdecoder";
import { useNavigate } from "react-router-dom";
//CNAMNILCPRNOIVIERCNOMGRENONCEPOCSEQ06CEXP2028-10CDTN2004-10-10CSEXMZQBR06466074-01


export const ScanCard: React.FC = () => {
  const [cardInfo, setCardinfo] = useState<CardData | null>(null);
  
  const navigate = useNavigate();
  
  useEffect(() => {
    // une fois que les données de la carte sont entrées, les données sont save dans le localStorage et on est redirigé vers la page /patientForm
    if (cardInfo){
      SaveData(cardInfo);
      navigate("/patientForm", {state: cardInfo});
    }
  }, [cardInfo]);
  
  return (
    <>
      <input
        type="text"
        name="RAMQ"
        autoFocus
        onChange={() => {}}
        onKeyDown={(e) => {
          // Si la touche enter est pressé, on save les informations dans la variable cardinfo
          if (e.key === 'Enter') {
            const value = (e.target as HTMLInputElement).value; // Récupère la valeur en temps réel saisie dans l'input
            setCardinfo(RAMQdecoder(value)); // Stock les infos de la RAMQ dans variable cardInfo
          }
        }}
        style={{ opacity: 0, position: 'absolute', pointerEvents: 'none' }}
      />
    </>
  );
};

export default ScanCard;
