import { BrowserRouter, Routes, Route } from "react-router-dom";
import React from "react";
import { Container, Stack } from "@mui/material";
import Accueil from "./pages/Accueil";
import Test from "./pages/test";
import PatientForm from "./pages/PatientForm";
import ScanCard from "./pages/ScanCard";
import Loading from "./pages/loading";


//Ajout des pages et des routes.
export default function App() {
  return (
    <BrowserRouter>
      <Container maxWidth={false} sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', flexDirection: 'column', backgroundColor: "secondary.main", height: '100vh', width: '100vw', position: 'relative' }}>
        <Stack spacing={2}>
          <Routes>
          <Route path="/clavier" element={<ScanCard />} />
          {/*<Route path="/formulaire" element={<FormulairePreAdmission />} />*/}
            <Route path="/scanner" element={<ScanCard />} />
            <Route path="/" element={<Accueil />} />
            <Route path="/test" element={<Test />} />
            <Route path="/PatientForm" element={<PatientForm />} />
            <Route path="/loading" element={<Loading />} />
          </Routes>
        </Stack>
      </Container>
    </BrowserRouter>
  );
}
