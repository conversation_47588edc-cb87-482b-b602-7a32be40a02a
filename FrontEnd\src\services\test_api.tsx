// Base URL de ton API locale (backend qui écoute sur le port 3002)
import axios from 'axios';
import type { AxiosResponse } from 'axios';

const API_BASE_URL: string = 'http://localhost:3002'; // URL de l'API

/**
 * Récupère les informations d'un utilisateur via son NAM
 * @param nam - Le numéro d'assurance maladie (identifiant)
 * @returns Les données utilisateur (JSON)
 */
export const fetchUserInfoByNAM = async (nam: string) => {
  try {
    // Appel GET à l'API backend /ramq/{nam}
    const response: AxiosResponse<any> = await axios.get(`${API_BASE_URL}/ramq/${encodeURIComponent(nam)}`, {
      headers: {
        'Accept': 'application/json', // On attend une réponse JSON
      },
    });

    // Parse la réponse en JSON
    const data = response.data;

    // Retourne les données à l'appelant
    return data;

  } catch (error: any) {
    // En cas d'erreur (r<PERSON>eau, serveur, etc.)
    console.error('Erreur API:', error.response?.status, error.message);
    throw error; // On relance l'erreur pour que l'appelant puisse la gérer
  }
};

/**
 * Met à jour les informations d'un utilisateur via son NAM
 * @param nam - Le numéro d'assurance maladie (identifiant)
 * @param dataToUpdate - Un objet contenant les champs à mettre à jour
 * @returns Les données mises à jour (JSON)
 */
export const modifyUserInfo = async (nam: string, dataToUpdate: object) => {
  try {
    // Appel PUT à l'API backend /ramq/{nam}
    const response: AxiosResponse<any> = await axios.put(
      `${API_BASE_URL}/ramq/${encodeURIComponent(nam)}`, {
      dataToUpdate});

    const updatedData = response.data;

    // Affiche dans la console (utile pour debug)
    console.log('Données modifiées:', updatedData);

    // Retourne les données mises à jour à l'appelant
    return updatedData;

  } catch (error) {
    // Capture et affiche les erreurs
    console.error('Erreur lors de la modification des données:', error);
    throw error;
  }
};
