import React, { useState } from "react";
import InformationContainer from "./InformationContainer";
import { Box, Typography } from "@mui/material";

interface InformationRDVProps {
 nomRdv: any;
  heurePrevue: any;
  salle: any;
  medecinPrenom: any;
  medecinNom: any;
  duree: any;
  prerequis: any;
}

export default function InformationRDV({ 
  nomRdv, 
  heurePrevue, 
  salle, 
  medecinPrenom, 
  medecinNom, 
  duree, 
  prerequis 
}: InformationRDVProps) {
  return (
    <InformationContainer title={nomRdv} displayButton={false}>
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, width: '100%', padding: 2 }}>
        <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between', width: '100%', mb: 0 }}>
          <Typography variant="body2" fontSize={"24px"} width={'50%'} paddingLeft={2}>
            Heure prévue:
          </Typography>
          <Typography variant="body2" fontSize={"24px"} width={'50%'}>
            {heurePrevue}
          </Typography>
        </Box>
        
        <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between', width: '100%', mb: 0 }}>
          <Typography variant="body2" fontSize={"24px"} width={'50%'} paddingLeft={2}>
            Salle:
          </Typography>
          <Typography variant="body2" fontSize={"24px"} width={'50%'}>
            {salle}
          </Typography>
        </Box>
        
        <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between', width: '100%', mb: 0 }}>
          <Typography variant="body2" fontSize={"24px"} width={'50%'} paddingLeft={2}>
            Médecin:
          </Typography>
          <Typography variant="body2" fontSize={"24px"} width={'50%'}>
            {medecinPrenom} {medecinNom}
          </Typography>
        </Box>
        
        <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between', width: '100%', mb: 0 }}>
          <Typography variant="body2" fontSize={"24px"} width={'50%'} paddingLeft={2}>
            Durée:
          </Typography>
          <Typography variant="body2" fontSize={"24px"} width={'50%'}>
            {duree}
          </Typography>
        </Box>
        
        <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between', width: '100%', mb: 0 }}>
          <Typography variant="body2" fontSize={"24px"} width={'50%'} paddingLeft={2}>
            Prérequis:
          </Typography>
          <Typography variant="body2" fontSize={"24px"} width={'50%'}>
            {prerequis}
          </Typography>
        </Box>
      </Box>
    </InformationContainer>
  );
}