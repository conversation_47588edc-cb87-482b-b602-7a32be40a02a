import InformationContainer from "./InformationContainer";
import { Typography, Box, TextField, Select, MenuItem } from "@mui/material";

export default function ContactUrgence() {
  return (
    <InformationContainer title="Contact d'urgence">
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, width: '100%', padding: 2 }}>
        <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between', width: '100%', mb: 0 }}>
          <TextField label="Prénom" variant="outlined" sx={{ width: '49%' }}/>
          <TextField label="Nom" variant="outlined" sx={{ width: '49%' }}/>
        </Box>
        <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between', width: '100%', mb: 0 }}>
            <Select label="Type" value='Type' variant="outlined" sx={{ width: '30%' }}>
            <MenuItem value={1}>Maison</MenuItem>
            <MenuItem value={2}>Cellulaire</MenuItem>
            <MenuItem value={3}>Travail</MenuItem>
            </Select>
            <TextField label="Numéro de téléphone" variant="outlined" sx={{ width: '69%' }}/>
        </Box>
      </Box>
    </InformationContainer>
  );
}