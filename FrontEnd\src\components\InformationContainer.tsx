import React, { useState, useEffect } from "react";
import {
  Box,
  Card,
  CardContent,
  Grid,
  Typography,
  IconButton,
} from "@mui/material";
import path756 from "../assets/path756.svg";

/**
 * @param title     titre du component
 * @param children  Le code MaterialUI qui sera contenue dans le composent
 * @returns         le code du composant
 */
const InformationContainer = ({ title, displayButton=true, children}: { title?: any, displayButton?: any, children?: any}) => {
  const [modifiable, setModifiable] = useState<boolean>(!displayButton);
  useEffect(() => {
    localStorage.setItem("modifiable", `${modifiable}`);
  }, [modifiable]);

  return (
    <Box sx={{ p: 2, width: "100%", maxWidth: "50vw", mx: "auto",}}>
      <Card
          sx={{
          borderTopLeftRadius: 0,
          borderTopRightRadius: 0,
          borderBottomLeftRadius: 16,
          borderBottomRightRadius: 16,
          overflow: "hidden",
          padding: 0,
          boxShadow: 0
        }}>
        {/* Header du composant */}
        <Box
          sx={{
          backgroundColor: "secondary.main",
          px: 2,
          py: 0,
          position: "relative"
          }}
        >
          {/* Titre du composant */}
          <Typography variant="subtitle1" sx={{ fontWeight: 600, color: "#fffefb", fontSize: '36px' }} >
            {title}
          </Typography>
           {/* Bouton modifier */}

           {!modifiable &&
            <IconButton onClick={() => {
                setModifiable(true)
                localStorage.setItem("modifiable", `${modifiable}`)
              }} sx={{ position: "absolute", top: 0, right:0 }}>
              <img
                src={path756}
                alt="Bouton image"
                style={{ width: 48, height: 48 }}
              />
            </IconButton>
           }
        </Box>
         {/* Card qui va contenir les éléments que l'ont voudra mettre dedans */}
        <CardContent sx={{ backgroundColor: "primary.main",  border: '3px solid', borderColor: 'secondary.main',
          borderRadius: 2,
          borderTopLeftRadius: 0,
          borderTopRightRadius: 0,
          borderTopWidth: 0,
          borderBottomLeftRadius: 16,
          borderBottomRightRadius: 16,
          padding: "0 !important",
          overflow: "hidden",
          
          }}>
          <Grid container spacing={0}>
            {children}
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
}

export default InformationContainer;