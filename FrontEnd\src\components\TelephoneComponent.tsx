import React, { useState } from "react";
import {
  Box,
  Select,
  TextField,
  MenuItem,
  Container
} from "@mui/material";
import InformationContainer from "./InformationContainer";


export default function TelephoneComponent({ telephone }: { telephone: any }) {
    const [value, setValue] = useState("maison")
    return (
        <>
            <InformationContainer title="Téléphone">
                <Box sx={{ display: "flex", flexDirection: "column", gap: 2, width: "100%", padding: 2 }}>
                    {telephone &&
                        telephone.map(({ number }: any, index: any) => (
                            <Box key={index} sx={{ display: "flex", flexDirection: "row", gap: 2}}>
                                <Select
                                    value={value}
                                    onChange={(e) => setValue(e.target.value)}
                                    displayEmpty
                                    sx={{ width: "40%" }}
                                >
                                        <MenuItem value="maison">Maison</MenuItem>
                                        <MenuItem value="travail">Travail</MenuItem>
                                        <MenuItem value="cellulaire">Cellulaire</MenuItem>
                                </Select>
                                <TextField label={number} sx={{
                                    "& .MuiInputLabel-root": { color: "black" },
                                    width: "100%"
                                }}>
                                </TextField>
                            </Box>
                        ))
                    }
                </Box>
            </InformationContainer>
        </>
    );
}