import React from "react";
import InformationContainer from "./InformationContainer";
import { TextField, Box, Typography } from "@mui/material";

export default function InformationParentsForm({ prenomMere, prenomPere, nomMere, nomPere,}: { prenomMere: any, prenomPere: any, nomMere: any, nomPere: any,}) {
  return (
    <InformationContainer
      title="Information parents"
    >
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, width: '100%', padding: 2 }}>
        <Box
          sx={{
            display: "flex",
            flexDirection: "row",
            justifyContent: "space-between",
            width: "100%",
            mb: 0,
          }}
        >
          <TextField
            label="Prénom Mère"
            variant="outlined"
            sx={{ width: "49%" }}
            InputLabelProps={{ sx: { color: "gray" } }}
            value={prenomMere}
          />
          <TextField
            label="Nom Mère"
            variant="outlined"
            sx={{ width: "49%" }}
            value={nomMere}
            InputLabelProps={{ sx: { color: "gray" } }}
          />
        </Box>

        <Box
          sx={{
            display: "flex",
            flexDirection: "row",
            justifyContent: "space-between",
            width: "100%",
            mb: 0,
          }}
        >
          <TextField
            label="Prénom Père"
            variant="outlined"
            sx={{ width: "49%" }}
            InputLabelProps={{ sx: { color: "gray" } }}
            value={prenomPere}
          />
          <TextField
            label="Nom Père"
            variant="outlined"
            sx={{ width: "49%" }}
            InputLabelProps={{ sx: { color: "gray" } }}
            value={nomPere}
          />
        </Box>
      </Box>
    </InformationContainer>
  );
}

