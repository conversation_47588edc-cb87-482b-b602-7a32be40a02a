import { Box } from "@mui/material";
import { styled, keyframes } from "@mui/material/styles";
import coeur from "../../images/coeur.svg";

const spin = keyframes`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`;

const Container = styled(Box)<{ size: number }>(({ size }) => ({
  position: "relative",
  width: `${size}px`,
  height: `${size}px`,
}));

// thin border circle (size includes the border; stays centered)
const BorderCircle = styled(Box)<{ size: number; thickness: number; color: string }>(
  ({ size, thickness, color }) => ({
    position: "absolute",
    top: "50%",
    left: "50%",
    width: `${size}px`,
    height: `${size}px`,
    borderRadius: "50%",
    border: `${thickness}px solid ${color}`,
    boxSizing: "border-box",
    transform: "translate(-50%, -50%)",
    zIndex: 3, // above the main ring so it's visible
    pointerEvents: "none",
  })
);

// the visible ring (static)
const Ring = styled(Box)<{ borderWidth: number; ringColor: string }>(({ borderWidth, ringColor }) => ({
  position: "absolute",
  inset: 0,
  borderRadius: "50%",
  border: `${borderWidth}px solid ${ringColor}`,
  boxSizing: "border-box",
  zIndex: 2,
}));

// orbit layer
const Orbit = styled(Box)<{ durationMs: number }>(({ durationMs }) => ({
  position: "absolute",
  inset: 0,
  borderRadius: "50%",
  animation: `${spin} ${durationMs}ms linear infinite`,
  transformOrigin: "50% 50%",
  zIndex: 4, // above everything so the heart is on top
}));

// place the heart on the ring’s midline
const HeartCarrier = styled(Box)<{ borderWidth: number; borderThickness?: number }>(({ borderWidth, borderThickness = 0 }) => ({
  position: "absolute",
  left: "50%",
  top: 0,
  // nudge so it sits on the ring's middle; include outer border thickness if you want it exactly centered
  transform: `translate(-50%, -50%) translateY(${borderWidth / 2 + borderThickness / 2}px)`,
  transformOrigin: "50% 50%",
}));

// upside-down heart
const HeartImg = styled("img")<{ heartSize: number }>(({ heartSize }) => ({
  width: `${heartSize}px`,
  height: `${heartSize}px`,
  display: "block",
  transform: "rotate(180deg)",
}));

interface LoadingSpinnerProps {
  width?: number;          // ring outer diameter
  borderWidth?: number;    // ring thickness
  ringColor?: string;      // ring color
  heartSize?: number;      // heart icon size
  speedMs?: number;        // orbit duration
  borderThickness?: number;// thin black edge thickness
  borderColor?: string;    // edge color
}

export default function LoadingSpinner({
  width = 40,
  borderWidth = 4,
  ringColor = "#fffefb",
  heartSize = 20,
  speedMs = 2000,
  borderThickness = 1,
  borderColor = "black",
}: LoadingSpinnerProps) {
  // --- sizing math (box-sizing: border-box) ---
  // Outer-edge stroke centered on the ring's OUTER edge:
  //   center radius = width/2  => sizeOuter = width + borderThickness
  const sizeOuter = width + borderThickness;

  // Inner-edge stroke centered on the ring's INNER edge:
  //   inner radius = width/2 - borderWidth
  //   center radius formula => sizeInner = width - 2*borderWidth + borderThickness
  const sizeInner = Math.max(0, width - 2 * borderWidth + borderThickness);

  return (
    <Container size={width}>
      {/* outer black edge */}
      <BorderCircle size={sizeOuter} thickness={borderThickness} color={borderColor} />

      {/* main ring */}
      <Ring borderWidth={borderWidth} ringColor={ringColor} />

      {/* inner black edge */}
      {sizeInner > 0 && (
        <BorderCircle size={sizeInner} thickness={borderThickness} color={borderColor} />
      )}

      {/* orbiting heart */}
      <Orbit durationMs={speedMs}>
        <HeartCarrier borderWidth={borderWidth} borderThickness={borderThickness}>
          <HeartImg src={coeur} alt="loading-heart" heartSize={heartSize} />
        </HeartCarrier>
      </Orbit>
    </Container>
  );
}
