import { Button, useTheme } from "@mui/material";
import { Link as MuiLink } from "@mui/material";

interface CustomButtonProps {
  href?: string;
  children: React.ReactNode;
  onClick?: () => void;
}
//Bouton customizable pour les formulaires (les couleurs changent)
export default function CustomButtonForm({ href, children, onClick }: CustomButtonProps) {
  return (
    <MuiLink href={href}>
      <Button
        variant="contained"
        size="large"
        onClick={onClick}
        sx={{
          width: '100%',
          height: '80%',
          fontSize: '32px',
          textAlign: 'center',
          color: 'background.default',
          backgroundColor: 'action.active',
          border: '2px solid',
          borderColor: 'primary.main',
          boxShadow: 'none',
          '&:hover': {
            backgroundColor: 'primary.main',
            border: '2px solid', 
            borderColor: 'action.active',
            color: 'action.active',
            boxShadow: 'none',
          },
          borderRadius: '500px',
          marginBottom: '0px',
          
        }}
      >
        {children}
      </Button>
    </MuiLink>
  );
}
