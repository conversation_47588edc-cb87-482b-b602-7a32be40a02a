import { <PERSON>, <PERSON><PERSON>, Container, <PERSON>ack, Typography } from "@mui/material";
import { Link as MuiLink } from "@mui/material";
import logo from "../../images/logos_noir.png";
import logo2 from "../../images/logos.png";
import ramq from "../../images/ramqEncerclé.png";
import FormGroup from '@mui/material/FormGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import DayNightSwitch from "../components/DayNightSwitch";
import LanguageSelector from "../components/LanguageSelector";
import { useColorScheme } from "@mui/material/styles";
import fleche from "../../images/fleche.svg";

//Page d'accueil avec gros bouton, texte, logo, etc.
export default function Accueil() {
  const { mode } = useColorScheme();
  return (
    <Container maxWidth={false} sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', flexDirection: 'column', backgroundColor: mode === 'dark' ? '#191c25' : '#fffefb', width: '100vw', height: '100vh', position: 'relative' }}>
      <FormGroup sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between', width: '90vw', position: 'absolute', top: 0, p: 7, paddingLeft: 0}}>
        <FormControlLabel
          control={<DayNightSwitch sx={{ m: 0, mt: 0 }} />}
          label={<Typography sx={{ fontFamily: 'Barlow', fontSize: '32px', paddingLeft: 2, fontWeight: 600 }}>Jour | Nuit</Typography>}
        />
        <LanguageSelector />
      </FormGroup>
      <Typography variant="h4" align="center" fontFamily={"Barlow"} sx={{ mb: 4, mt: 0 }}>Bienvenue chez Clinique de Radiologie. </Typography>
      <Typography variant="h1" align="center" fontFamily={"Barlow"} fontWeight={400} sx={{ fontSize: '5rem', mb: 8, width: '80vw' }}><Box component={"span"} sx={{ fontWeight: 600 }}>Scannez</Box> votre carte d'assurance maladie<br/>pour <Box component={"span"} sx={{ fontWeight: 600 }}>votre préadmission</Box></Typography>
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', position: 'relative' }}>
        <MuiLink href="/loading">
            <Button variant="contained" size="large" sx={{
            width: '628.2196px',
            height: '118.72px',
            fontSize: '38.16px',
            textAlign: 'center',
            color: 'background.default',
            backgroundColor: 'action.active',
            border: '2px solid',
            borderColor: 'background.default',
            boxShadow: 'none',
            '&:hover': {
              backgroundColor: 'background.default',
              border: '2px solid', 
              borderColor: 'action.active',
              color: 'action.active',
              boxShadow: 'none',
            },
            borderRadius: '500px',
            marginBottom: '0px',
          }}>Scanner la carte</Button>
        </MuiLink>
        <Box sx={{ position: 'absolute', top: '40%', left: '88%', transform: 'translate(-50%, -50%)', width: '50px', height: '100px' }}>
          <img src={fleche} alt="fleche" style={{width: '100%', height: '100%', objectFit: 'contain', transform: 'scaleX(-1) rotate(-225deg)'}}/>
        </Box>
        <Typography variant="h7" align="center" fontFamily={"Barlow"} sx={{ mt: 2, mb: 2 }}> *Pour toute <Box component={"span"} sx={{ fontWeight: 600 }}>assistance</Box>, veuillez contacter la <Box component={"span"} sx={{ fontWeight: 600 }}>réception</Box>.</Typography>
        <Container sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', width: '33vw', flexDirection: 'row', padding: '0px', margin: '0px' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', width: '100%', height: '100%' }}>
              <img src={ramq} alt="ramq" style={{width: '750px', objectFit: 'contain'}}/>
          </Box>
        </Container>
        </Box>
        <Box sx={{ width: "256px", position: 'absolute', bottom: 15}}>
          <img src={mode === 'dark' ? logo2 : logo} alt="logo" style={{width: '100%', height: '100%', objectFit: 'contain'}}/>
        </Box>
        
        
    </Container>
  );
}
