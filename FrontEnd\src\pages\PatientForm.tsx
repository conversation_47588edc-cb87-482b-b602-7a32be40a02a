import { FormControl, TextField, Button,  Link as <PERSON><PERSON><PERSON><PERSON>, Typo<PERSON>, Container, Stack, Box, Select, InputLabel, MenuItem } from '@mui/material';
import { useEffect, useState, useRef } from 'react';
import { type Addresse, type PhoneNumber, type CardData} from "../types/ClientData";
import { fetchUserInfoByNAM } from "../api/ramq";
import TelephoneComponent from "../components/TelephoneComponent"
import InfosRamqFormulaire from "../components/InfosRamqFormulaire"
import AdresseFormulaire from "../components/AdresseFormulaire"
import CustomButtonForm from '../components/CustomButtonForm';
import InformationParents from '../components/InformationParents';
import MedecinFamForm from "../components/InformationMedecinFam";
import SideLogo from "../components/SideLogo";
import React from 'react';
import { Height, WidthFull } from '@mui/icons-material';
import ContactUrgence from '../components/ContactUrgence';
import ResumeDossier from '../components/ResumeDossier';
import InformationAllergies from '../components/InformationAllergies';
import InformationRDV from '../components/InformationRdv';
import { fetchUserAppointments } from '../api/rdv';

//Affichage du formulaire avec des champs (barrés, non barrés)
export default function PatientForm() {
  const [counter, setCounter] = useState(0);
  // Variable qui permet de mettre le formulaire en mode modification
  const [isDisabled, setIsDisabled] = useState(true);
  // variables récupérés de la BD
  const [email, setEmail] = useState<string>();
  const [middleName, setMiddleName] = useState<string>();
  const [motherFirstName, setMotherFirstName] = useState<string>();
  const [motherLastName, setMotherLastName] = useState<string>();
  const [fatherFirstName, setFatherFirstName] = useState<string>();
  const [fatherLastName, setFatherLastName] = useState<string>();
  const [conjunctFirstName, setConjunctFirstName] = useState<string>();
  const [conjunctLastName, setConjunctLastName] = useState<string>();
  const [primaryLocation, setPrimaryLocation] = useState<string>();
  const [currentLocation, setCurrentLocation] = useState<string>();
  const [treatingPhysicianFirstName, setTreatingPhysicianFirstName] = useState<string>();
  const [treatingPhysicianLastName, setTreatingPhysicianLastName] = useState<string>();  
  const [addresses, setAddresses] = useState<Addresse[] | undefined >();
  const [phoneNumbers, setPhoneNumbers] = useState<PhoneNumber[] | undefined >();
  const [localData, setLocalData] = useState<CardData>();

  const [nomRdv, setNomRdv] = useState<string>("");
  const [heurePrevue, setHeurePrevue] = useState<string>("");
  const [salle, setSalle] = useState<string>("");
  const [medecinPrenomRDV, setMedecinPrenomRDV] = useState<string>("");
  const [medecinNomRDV, setMedecinNomRDV] = useState<string>("");
  const [duree, setDuree] = useState<string>("");
  const [prerequis, setPrerequis] = useState<string>("");
  // Permet de bloquer la deuxieme execution (StrictMode)
  const didRunRef = useRef(false);
  //on clique sur next pour faire +1
  const handleNext = () => {
    if (counter === 1) {
      setCounter(8);
      return;
    }
    setCounter(prev => prev + 1);
  };
  //on clique sur back pour faire -1
  const handleBack = () => {
    if (counter === 1) {
      setCounter(2);
      return;
    }
    setCounter(prev => prev - 1);
  };
  //toggle titre
  const [showTitle, setShowTitle] = useState(true);

  useEffect(() => {
    if (didRunRef.current) return; // bloque la 2e exécution (StrictMode)
    didRunRef.current = true;
    
    //convertie les données du localStorage en JSON
    const localDatas: CardData = JSON.parse(localStorage.getItem("infosClient") ?? "{}");
    setLocalData(localDatas);
    /**
     * ajoute un numero de telephone aux listes de numeros de telephone
     * @param phone l'objet telephone
     * @returns rien (void)
     */
    const addPhoneNumber = (phone: PhoneNumber): void => {
      const phoneNumber: PhoneNumber = {
        number: phone.number,
        extention: phone.extention,
        category: phone.category,
      }
      
      setPhoneNumbers(prev => [...(prev ?? []), phoneNumber]); // Ajoute un nouveau tableau s'il y a pas de valeur dans le tableau, sinon, ajoute le numero de telephone
    }
    
    /**
     * ajoute une address aux listes d'address
     * @param addr l'objet address
     * @returns rien (void)
     */
    const addAddress = (addr: Addresse): void => {

      const address: Addresse = {
        civicNumber: addr.civicNumber,
        street: addr.street,
        city: addr.city,
        country: addr.country,
        state: addr.state,
        postalCode: addr.postalCode,
        description: addr.description,
        office: addr.office,
      };

      // Ajoute un nouveau tableau s'il y a pas de valeur dans le tableau, sinon, ajoute l'address
      setAddresses(prev => [...(prev ?? []), address]);
    }

    const getData = async() => {
      try {
        
        // Récupere les infos du client
        const APIdata = await fetchUserInfoByNAM(localDatas.nam);
        
        setEmail(APIdata.email);
        setMiddleName(APIdata.middleName);
        setMotherFirstName(APIdata.motherFirstName);
        setMotherLastName(APIdata.motherLastName);
        setFatherFirstName(APIdata.fatherFirstName);
        setFatherLastName(APIdata.fatherLastName);
        setTreatingPhysicianFirstName(APIdata.treatingPhysicianFirstName);
        setTreatingPhysicianLastName(APIdata.treatingPhysicianLastName);
        setConjunctFirstName(APIdata.conjunctFirstName);
        setConjunctLastName(APIdata.conjunctLastName);
        setPrimaryLocation(APIdata.primaryLocation);
        setCurrentLocation(APIdata.currentLocation);

        // S'il y a plus d'une address
        if (APIdata?.addresses) {
          addAddress(APIdata.addresses[0])
        }
        // S'il y a plus de deux numeros de telephone
        if (APIdata?.phoneNumbers?.length >= 2) {
          APIdata.phoneNumbers.forEach((phone: PhoneNumber) => {
            addPhoneNumber(phone);
          });
        } else {
          console.log("APIdata?.phoneNumbers[0]")
          addPhoneNumber(APIdata?.phoneNumbers[0]);
        }
        // Fetch appointments
        if (APIdata.uid) {
          try {
            const appointmentsResponse = await fetchUserAppointments('54828');
            
            // Check if the response has an error property
            if (appointmentsResponse.error) {
              console.log("No appointments found:", appointmentsResponse.error);
              // Keep all values as empty strings - component will show "no appointment" message
            } else if (appointmentsResponse.success && appointmentsResponse.data.length > 0) {
              const firstOrder = appointmentsResponse.data[0];
              const firstProcedure = firstOrder.requestedProcedures[0];
              
              // Extract and format time
              const date = new Date(firstProcedure.scheduledStartExamDatetime);
              setHeurePrevue(date.toLocaleTimeString('fr-CA', { hour: '2-digit', minute: '2-digit' }));
              setNomRdv(firstOrder.requestingServiceUid);
              setSalle(firstOrder.siteUid || "À déterminer");
              setMedecinPrenomRDV(firstOrder.scheduledPerformingPhysician?.split(' ')[0] || "À");
              setMedecinNomRDV(firstOrder.scheduledPerformingPhysician?.split(' ')[1] || "assigner");
              setDuree("30 minutes");
              setPrerequis(firstOrder.patientSpecialNeeds || "Aucun");
            }
          } catch (error) {
            console.error("Error fetching appointments:", error);
            // Component will gracefully show "no appointment" message
          }
      }} catch (err) {
        console.error("API error :" + err);
      }
    }

    getData();
  }, []);



  const toggleFields = () => {
    setIsDisabled(!isDisabled);
  };

  // Cache le titre une fois que l'on est sur le counter 1
  useEffect(() => {
    if (counter === 1) {
      setShowTitle(false);
    } else {
      setShowTitle(true);
    }
  }, [counter]);
  
  return (
    <Container maxWidth={false} sx={{ display: 'flex', justifyContent: 'top', alignItems: 'center', flexDirection: 'column', backgroundColor: "primary.main", height: '100vh', width: '100vw', position: 'relative' }}>
        <SideLogo />
        {/*Box pour le titre */}
        <Box sx={{height: '91vh'}}>
          {showTitle && 
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '25vh', width: '100vw', mt: 2, mb: 3 }}>
              <Typography variant="h1" sx={{ fontSize: "9rem", mb: 0, mt: 0  }}>Confirmez</Typography>
              <Typography variant="h1" sx={{ fontWeight: 600, mt: -3, fontSize: "9rem" }}>votre dossier</Typography>
            </Box>
          }
          {/*Box pour le formulaire, ajouter les components nécessaires plus tard */}
            
          <Box sx={{ width: '50vw', mb: 12, justifySelf: 'center' }}>
            {counter == 0 && localData &&
              
              <InfosRamqFormulaire ramq={localData}/>
            }
            {counter == 1 && 
              <Box sx={{height: '15vh'}}>
              </Box>
            }
            
            {counter == 1 && 
              <ResumeDossier adresse={addresses[0]} telephone={phoneNumbers} medecinNom={treatingPhysicianFirstName} medecinPrenom={treatingPhysicianLastName} prenomMere={motherFirstName} nomMere={motherLastName} prenomPere={fatherFirstName} nomPere={fatherLastName} email={email}/>
            }
            {counter == 2 &&
              <AdresseFormulaire adresse={addresses[0]}/>
            }
            {counter == 3 &&
              <TelephoneComponent telephone={phoneNumbers}/>
            }
            {counter == 4 &&
              <InformationParents prenomMere={motherLastName} nomMere={motherFirstName} prenomPere={fatherFirstName} nomPere={fatherFirstName}/>
            }
            {counter == 5 &&
              <MedecinFamForm medecinNom={treatingPhysicianFirstName} medecinPrenom={treatingPhysicianLastName}/>
            }
            {counter == 6 &&
              <ContactUrgence />
            }
            {counter == 7 &&
              <InformationAllergies />
              
            }
            {counter == 8 &&
              <InformationRDV nomRdv={nomRdv} heurePrevue={heurePrevue} salle={salle} medecinPrenom={medecinPrenomRDV} medecinNom={medecinNomRDV} duree={duree} prerequis={prerequis}/>
            }
            </Box>
          </Box>
          {/*Box pour les boutons de navigation, marchent avec un compteur */}
          <Box sx={{display: 'flex', justifyContent: counter === 0 ? 'center' : 'space-between', width: '28vw', height:'9vh', position: 'sticky', bottom: 20, left: '50%', transform: 'translate(-50%, -50%)' }}>
            {counter > 0 && (
              <Box sx={{width: "250px"}}>
                <CustomButtonForm onClick={handleBack} >
                  {counter === 1 ? "MODIFIER" : "RETOUR"}
                </CustomButtonForm>
              </Box>
            )}
            <Box sx={{width: "250px"}}>
              <CustomButtonForm onClick={handleNext}>
                {counter === 1 ? "CONFIRMER" : "SUIVANT"}
              </CustomButtonForm>
            </Box>
          </Box>
        
    </Container>
  );
}
