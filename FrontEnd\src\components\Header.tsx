import { AppB<PERSON>, Too<PERSON>bar, <PERSON>po<PERSON>, <PERSON>, Button } from "@mui/material";
import { Link as RouterLink } from "react-router-dom";
//ne sert pas
export default function Header() {
   return (
      <AppBar position="static" enableColorOnDark>
         <Toolbar>
            <Typography variant="h6" sx={{ flexGrow: 1 }}>
            </Typography>
         </Toolbar>
      </AppBar>
   );
}