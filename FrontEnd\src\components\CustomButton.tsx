import { Button, useTheme } from "@mui/material";
import { Link as MuiLink } from "@mui/material";

interface CustomButtonProps {
  href: string;
  children: React.ReactNode;
  onClick?: () => void;
}
//Bouton customizable
export default function CustomButton({ href, children, onClick }: CustomButtonProps) {
  return (
    <MuiLink href={href}>
      <Button
        variant="contained"
        size="large"
        onClick={onClick}
        sx={{
          width: '100%',
          height: '100%',
          fontSize: '38.16px',
          textAlign: 'center',
          color: 'background.default',
          backgroundColor: 'action.active',
          border: '2px solid',
          borderColor: 'secondary.main',
          boxShadow: 'none',
          '&:hover': {
            backgroundColor: 'secondary.main',
            border: '2px solid', 
            borderColor: 'action.active',
            color: 'action.active',
            boxShadow: 'none',
          },
          borderRadius: '500px',
          marginBottom: '0px',
          
        }}
      >
        {children}
      </Button>
    </MuiLink>
  );
}
