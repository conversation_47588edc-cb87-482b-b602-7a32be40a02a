 
import { Box, TextField } from '@mui/material';
import InformationContainer from './InformationContainer';
import React from "react"


//Affichage du formulaire avec des champs (barrés, non barrés)
 
export default function InfosRamqFormulaire({ ramq }: { ramq: any }) {
  return (
    <InformationContainer title="Informations de la RAMQ" displayButton={false}>
      <Box display="flex" flexDirection="column" gap={2} width="100%" sx={{padding: 2}}>
        <Box width="100%">
          <TextField
            label="Numéro d'assurance"
            variant="outlined"
            value={ramq.nam}
            fullWidth
            sx={{ background: 'white', borderRadius: '30px'}}
            slotProps={{ input: { style: { borderRadius: 30, color: '#000' } }, inputLabel: { style: { color: '#000' } } }}
          />
        </Box>
        <Box display="flex" gap={2}>
          <TextField
            label="Prénom"
            variant="outlined"
            value={ramq.prenom}
            sx={{flex: 1, background: 'white', borderRadius: '30px'}}
            slotProps={{ input: { style: { borderRadius: 30, color: '#000' } }, inputLabel: { style: { color: '#000' } } }}
          />
          <TextField
            label="Nom"
            variant="outlined"
            value={ramq.nom}
            sx={{flex: 1, background: 'white', borderRadius: '30px'}}
            slotProps={{ input: { style: { borderRadius: 30, color: '#000' } }, inputLabel: { style: { color: '#000' } } }}
          />
        </Box>
        <Box display="flex" gap={2}>
          <TextField
            label="Date de naissance"
            variant="outlined"
            value={ramq.dateNaissance}
            sx={{flex: 1, background: 'white', borderRadius: '30px'}}
            slotProps={{ input: { style: { borderRadius: 30, color: '#000' } }, inputLabel: { style: { color: '#000' } } }}
          />
          <TextField
            label="Date d'expiration"
            variant="outlined"
            value={ramq.dateExpiration}
            sx={{flex: 1, background: 'white', borderRadius: '30px'}}
            slotProps={{ input: { style: { borderRadius: 30, color: '#000' } }, inputLabel: { style: { color: '#000' } } }}
          />
        </Box>
        <Box display="flex" justifyContent="flex-start">
          <TextField
            label="Sexe"
            variant="outlined"
            value={ramq.sexe}
            sx={{width: '120px', background: 'white', borderRadius: '30px'}}
            slotProps={{ input: { style: { borderRadius: 30, color: '#000' } }, inputLabel: { style: { color: '#000' } } }}
          />
        </Box>
      </Box>
    </InformationContainer>
  );
}