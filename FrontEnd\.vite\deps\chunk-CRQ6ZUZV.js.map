{"version": 3, "sources": ["../../node_modules/@mui/material/esm/utils/capitalize.js", "../../node_modules/@mui/material/esm/utils/memoTheme.js", "../../node_modules/@mui/material/esm/utils/isMuiElement.js", "../../node_modules/@mui/utils/esm/useForkRef/useForkRef.js"], "sourcesContent": ["import capitalize from '@mui/utils/capitalize';\nexport default capitalize;", "import { unstable_memoTheme } from '@mui/system';\nconst memoTheme = unstable_memoTheme;\nexport default memoTheme;", "import isMuiElement from '@mui/utils/isMuiElement';\nexport default isMuiElement;", "'use client';\n\nimport * as React from 'react';\n\n/**\n * Merges refs into a single memoized callback ref or `null`.\n *\n * ```tsx\n * const rootRef = React.useRef<Instance>(null);\n * const refFork = useForkRef(rootRef, props.ref);\n *\n * return (\n *   <Root {...props} ref={refFork} />\n * );\n * ```\n *\n * @param {Array<React.Ref<Instance> | undefined>} refs The ref array.\n * @returns {React.RefCallback<Instance> | null} The new ref callback.\n */\nexport default function useForkRef(...refs) {\n  const cleanupRef = React.useRef(undefined);\n  const refEffect = React.useCallback(instance => {\n    const cleanups = refs.map(ref => {\n      if (ref == null) {\n        return null;\n      }\n      if (typeof ref === 'function') {\n        const refCallback = ref;\n        const refCleanup = refCallback(instance);\n        return typeof refCleanup === 'function' ? refCleanup : () => {\n          refCallback(null);\n        };\n      }\n      ref.current = instance;\n      return () => {\n        ref.current = null;\n      };\n    });\n    return () => {\n      cleanups.forEach(refCleanup => refCleanup?.());\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n  return React.useMemo(() => {\n    if (refs.every(ref => ref == null)) {\n      return null;\n    }\n    return value => {\n      if (cleanupRef.current) {\n        cleanupRef.current();\n        cleanupRef.current = undefined;\n      }\n      if (value != null) {\n        cleanupRef.current = refEffect(value);\n      }\n    };\n    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler -- intentionally ignoring that the dependency array must be an array literal\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n}"], "mappings": ";;;;;;;;;;;;;AACA,IAAO,qBAAQ;;;ACAf,IAAM,YAAY;AAClB,IAAO,oBAAQ;;;ACDf,IAAO,uBAAQ;;;ACCf,YAAuB;AAiBR,SAAR,cAA+B,MAAM;AAC1C,QAAM,aAAmB,aAAO,MAAS;AACzC,QAAM,YAAkB,kBAAY,cAAY;AAC9C,UAAM,WAAW,KAAK,IAAI,SAAO;AAC/B,UAAI,OAAO,MAAM;AACf,eAAO;AAAA,MACT;AACA,UAAI,OAAO,QAAQ,YAAY;AAC7B,cAAM,cAAc;AACpB,cAAM,aAAa,YAAY,QAAQ;AACvC,eAAO,OAAO,eAAe,aAAa,aAAa,MAAM;AAC3D,sBAAY,IAAI;AAAA,QAClB;AAAA,MACF;AACA,UAAI,UAAU;AACd,aAAO,MAAM;AACX,YAAI,UAAU;AAAA,MAChB;AAAA,IACF,CAAC;AACD,WAAO,MAAM;AACX,eAAS,QAAQ,gBAAc,aAAa,CAAC;AAAA,IAC/C;AAAA,EAEF,GAAG,IAAI;AACP,SAAa,cAAQ,MAAM;AACzB,QAAI,KAAK,MAAM,SAAO,OAAO,IAAI,GAAG;AAClC,aAAO;AAAA,IACT;AACA,WAAO,WAAS;AACd,UAAI,WAAW,SAAS;AACtB,mBAAW,QAAQ;AACnB,mBAAW,UAAU;AAAA,MACvB;AACA,UAAI,SAAS,MAAM;AACjB,mBAAW,UAAU,UAAU,KAAK;AAAA,MACtC;AAAA,IACF;AAAA,EAGF,GAAG,IAAI;AACT;", "names": []}