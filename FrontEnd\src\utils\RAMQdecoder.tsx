// Type qui représente les données dans une carte de RAMQ
export type CardData = {
    nam: string;
    nom: string;
    prenom: string;
    dateNaissance: string;
    sexe: string;
    dateExpiration: string;
};
 /**
 * Récupère l'information(data) qui passé en paramètre du 
 * @param start - La chaine de charactères qui identifie la donnée à aller chercher
 * @param   end - La chaine de charactères qui identifie la donnée à aller chercher
 * @param  data - La chaine de charactères dans laquelle on va aller chercher l'info
 * @return La donnée à aller chercher (string)
 */
const getInfo = (start: string, end: string, data: string): string => {
    if (!data){
        console.error(`No data`);
        return "";
    }
    let decodedData: string[] = data.split(start);
    
    decodedData = decodedData[1].split(end);
 
    return decodedData[0];
}
 /**
 * Vérifi si la chaine retourné par le scanneur est complète
 * @param  data - La chaine de charactères dans laquelle on va aller vérifier si elle est complète
 * @return vrai si la chaine de caractere contient tout les infos sinon faux (boolean)
 */
const verifyInfo = (data: string): boolean => {
    return data.includes("CNAM") && data.includes("CPRN") 
           && data.includes("CNOM") && data.includes("CEXP")
           && data.includes("CEXP") && data.includes("CDTN")
           && data.includes("CSEX");
}
 /**
 * Utilise la fonction getInfo pour aller récupérer toutes les infos de la cartes
 * @param  data - La chaine de charactères dans laquelle on va aller chercher les infos
 * @return Les infos de la carte RAMQ (CardData)
 */
//CNAMBRAR47032416CPRNOLIVIERCNOMGRENONCEPOCSEQ06CEXP2028-10CDTN2004-10-10CSEXMZQBR06466074-01
export const RAMQdecoder = (data: string): CardData | null => {
    if (!verifyInfo(data)) {
        console.error("Les informations de la carte sont incomplètes!");
        return null;
    }
    const CNAM: string = getInfo("CNAM", "CPRN", data);
    const CPRN: string = getInfo("CPRN", "CNOM", data);
    const CNOM: string = getInfo("CNOM", "CEPO", data);
    const CEXP: string = getInfo("CEXP", "CDTN", data);
    const CDTN: string = getInfo("CDTN", "CSEX", data);
    const CSEX: string = getInfo("CSEX", "ZQBR", data);
    
    return {
        nam: CNAM,
        prenom: CPRN,
        nom: CNOM,
        dateNaissance: CDTN,
        dateExpiration: CEXP,
        sexe: CSEX,
    };
}