import { TextField, useTheme } from "@mui/material";

interface CustomTextFieldProps {
  label: string;
  value: string;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean;
}

//TextField customizable pour les formulaires
export default function CustomTextField({ label, value, onChange, disabled }: CustomTextFieldProps) {
  const theme = useTheme();
  return (
    <TextField
      label={label}
      value={value}
      onChange={onChange}
      disabled={disabled}
      sx={{
        backgroundColor: 'text.secondary',
        borderRadius: '10px',
        '& .MuiInputBase-input': {
          color: 'text.primary',
        },
        '& .MuiInputLabel-root': {
          color: 'text.primary',
        },
        borderColor: 'text.primary',
        borderWidth: '2px',
        borderStyle: 'solid',
      }}
    />
  );
}
