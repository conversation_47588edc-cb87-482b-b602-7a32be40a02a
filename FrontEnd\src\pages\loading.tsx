import React from "react";
import LoadingSpinner from "../components/LoadingSpinner";
import { useColorScheme } from "@mui/material/styles";
import { Container, Button, Link as MuiLink, Box, useTheme } from "@mui/material";
import CustomButton from "../components/CustomButton";
import Side<PERSON>ogo from "../components/SideLogo";
import { ScanCard } from "./ScanCard";

//Page de chargement
export default function Loading() {
  const { mode } = useColorScheme();
  const theme = useTheme(); // pour récupérer la couleur du mode actuel
  return (
    <Container maxWidth={false} sx={{  
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      height: '100vh', 
      flexDirection: 'column', 
      width: '100vw', 
      backgroundColor: "secondary.main", 
      position: 'fixed', 
      top: 0, 
      left: 0, 
      margin: 0, 
      padding: 0,
    }}>
      <SideLogo />
      <Box sx={{ position: 'fixed' , top: '45%', left: '50%', transform: 'translate(-50%, -50%)' }}>
        <LoadingSpinner width={424} borderWidth={42.4} ringColor={theme.palette.text.secondary} heartSize={48} speedMs={2000}/>
      </Box>
      <Box sx={{position: 'absolute',
          bottom: 0,
          left: '50%',
          transform: 'translate(-50%, -180%)', width:'480px', height:'80px'}}>
        <CustomButton href="/">ANNULER</CustomButton>
      </Box>
      <ScanCard />
    </Container>
);;
}
