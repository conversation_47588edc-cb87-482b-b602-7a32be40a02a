import logo from "../../images/logos_noir.png";
import logo2 from "../../images/logos.png";
import { useColorScheme } from "@mui/material/styles";
import { Box } from "@mui/material";
//Logo sur le côté à ajouter dans les pages
export default function SideLogo() {
  const { mode } = useColorScheme();
  return (
    <Box sx={{ 
      width: "100px", 
      height: "100vh", 
      position: 'fixed', 
      top: 0, 
      left: 0,
      borderRight: '1px solid',
      borderColor: 'text.main',
      display: 'flex',
      alignItems: 'flex-start',
      justifyContent: 'center',
      paddingTop: 25
    }}>
      <img 
        src={mode === 'dark' ? logo2 : logo} 
        alt="logo" 
        style={{
          height: 100, 
          objectFit: 'contain',
          transform: 'rotate(270deg)'
        }}
      />
    </Box>
  );
}
